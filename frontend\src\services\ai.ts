import axios from 'axios'
import { RpaFlow, RpaStep, ApiResponse } from '@rpa-project/shared'
import { authService } from './auth'

const API_BASE_URL = '/api/ai-assistant'

const aiApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for authentication and logging
aiApi.interceptors.request.use((config) => {
  console.log(`AI API Request: ${config.method?.toUpperCase()} ${config.url}`)

  // Add authentication header
  const authHeader = authService.getAuthHeader();
  if (authHeader) {
    config.headers.Authorization = authHeader;
  }

  return config
})

// Response interceptor for error handling and auth
aiApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('AI API Error:', error.response?.data || error.message)

    // Handle authentication errors
    if (error.response?.status === 401) {
      console.log('🔐 AI API Authentication error detected');
      authService.handleAuthError(error);
    }

    return Promise.reject(error)
  }
)

// Types for AI assistant requests
export interface GenerateFlowRequest {
  prompt: string
  flowName?: string
  description?: string
}

export interface SuggestNextStepRequest {
  flowId?: string
  currentSteps: RpaStep[]
  prompt: string
}

export interface OptimizeFlowRequest {
  flowId: string
  currentSteps: RpaStep[]
  optimizationGoal?: 'speed' | 'reliability' | 'maintainability' | 'general'
}

export interface DebugFlowRequest {
  flowId: string
  currentSteps: RpaStep[]
  errorDescription: string
  executionLogs?: Array<{
    level: string
    message: string
    timestamp?: string
  }>
}

export interface DebugFlowResponse {
  steps: RpaStep[]
  explanation: string
}

export interface EditStepRequest {
  step: RpaStep
  prompt: string
  context?: RpaStep[]
}

export interface AIHealthStatus {
  aiStatus: 'connected' | 'error' | 'not_configured'
  hasApiKey: boolean
  timestamp: string
}

// AI Assistant API functions
export const aiService = {
  /**
   * Generate a complete RPA flow from a natural language description
   */
  async generateFlow(request: GenerateFlowRequest): Promise<RpaFlow> {
    const response = await aiApi.post<ApiResponse<RpaFlow>>('/generate-flow', request)
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to generate flow')
    }
    
    return response.data.data!
  },

  /**
   * Suggest next steps for an existing flow
   */
  async suggestNextStep(request: SuggestNextStepRequest): Promise<RpaStep[]> {
    const response = await aiApi.post<ApiResponse<RpaStep[]>>('/suggest-next-step', request)
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to suggest next step')
    }
    
    return response.data.data!
  },

  /**
   * Optimize an existing flow for speed, reliability, or maintainability
   */
  async optimizeFlow(request: OptimizeFlowRequest): Promise<RpaStep[]> {
    const response = await aiApi.post<ApiResponse<RpaStep[]>>('/optimize-flow', request)
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to optimize flow')
    }
    
    return response.data.data!
  },

  /**
   * Debug flow issues and get corrected steps
   */
  async debugFlow(request: DebugFlowRequest): Promise<DebugFlowResponse> {
    const response = await aiApi.post<ApiResponse<DebugFlowResponse>>('/debug-flow', request)
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to debug flow')
    }
    
    return response.data.data!
  },

  /**
   * Edit a specific step based on a prompt
   */
  async editStep(request: EditStepRequest): Promise<RpaStep> {
    const response = await aiApi.post<ApiResponse<RpaStep>>('/edit-step', request)

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to edit step')
    }

    return response.data.data!
  },

  /**
   * Check AI assistant health and configuration
   */
  async checkHealth(): Promise<AIHealthStatus> {
    const response = await aiApi.get<ApiResponse<AIHealthStatus>>('/health')

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to check AI health')
    }

    return response.data.data!
  }
}

// Helper functions for common AI operations
export const aiHelpers = {
  /**
   * Check if AI assistant is properly configured
   */
  async isConfigured(): Promise<boolean> {
    try {
      const health = await aiService.checkHealth()
      return health.aiStatus === 'connected'
    } catch {
      return false
    }
  },

  /**
   * Generate a flow with error handling and user feedback
   */
  async generateFlowSafely(
    prompt: string, 
    options?: { flowName?: string; description?: string }
  ): Promise<{ success: boolean; flow?: RpaFlow; error?: string }> {
    try {
      if (!prompt.trim()) {
        return { success: false, error: 'Prompt cannot be empty' }
      }

      const flow = await aiService.generateFlow({
        prompt: prompt.trim(),
        flowName: options?.flowName,
        description: options?.description
      })

      return { success: true, flow }
    } catch (error) {
      console.error('Error generating flow:', error)
      
      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          return { success: false, error: 'Ebbot is not properly configured. Please check API settings.' }
        }
        if (error.message.includes('rate limit')) {
          return { success: false, error: 'Too many requests. Please try again in a moment.' }
        }
        return { success: false, error: error.message }
      }
      
      return { success: false, error: 'An unexpected error occurred while generating the flow' }
    }
  },

  /**
   * Suggest next steps with error handling
   */
  async suggestNextStepSafely(
    currentSteps: RpaStep[], 
    prompt: string, 
    flowId?: string
  ): Promise<{ success: boolean; steps?: RpaStep[]; error?: string }> {
    try {
      if (!prompt.trim()) {
        return { success: false, error: 'Prompt cannot be empty' }
      }

      if (!currentSteps || currentSteps.length === 0) {
        return { success: false, error: 'No current steps provided' }
      }

      const steps = await aiService.suggestNextStep({
        currentSteps,
        prompt: prompt.trim(),
        flowId
      })

      return { success: true, steps }
    } catch (error) {
      console.error('Error suggesting next step:', error)
      
      if (error instanceof Error) {
        return { success: false, error: error.message }
      }
      
      return { success: false, error: 'An unexpected error occurred while suggesting next steps' }
    }
  },

  /**
   * Format AI error messages for user display
   */
  formatErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return 'Ebbot is not configured. Please contact your administrator.'
      }
      if (error.message.includes('rate limit')) {
        return 'Too many requests. Please wait a moment and try again.'
      }
      if (error.message.includes('network') || error.message.includes('timeout')) {
        return 'Network error. Please check your connection and try again.'
      }
      return error.message
    }
    
    return 'An unexpected error occurred'
  }
}

export default aiService
