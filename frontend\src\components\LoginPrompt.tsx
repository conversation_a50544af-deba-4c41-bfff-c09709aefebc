import React, { useState } from 'react';
import { authService } from '../services/auth';

interface LoginPromptProps {
  onLogin: () => void;
}

export const LoginPrompt: React.FC<LoginPromptProps> = ({ onLogin }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin123!');
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      await authService.login(username, password);
      console.log('🔐 Login successful');
      onLogin();
    } catch (error: any) {
      console.error('Login failed:', error);
      setError(error.message || 'Inloggning misslyckades');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickLogin = () => {
    setUsername('admin');
    setPassword('admin123!');
  };

  return (
    <div className="login-page">
      <div className="login-container">
        {/* Left side - Login form */}
        <div className="login-form-section">
          <div className="login-form-container">
            <div className="login-header">
              <div className="login-logo">
                <div className="login-logo-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h1 className="login-title">Automation Hub</h1>
              </div>
              <div className="login-subtitle">
                <h2>Välkommen tillbaka</h2>
                <p>Logga in för att komma åt din automatiseringsplattform</p>
              </div>
            </div>

            <form onSubmit={handleLogin} className="login-form">
              {error && (
                <div className="login-error">
                  <div className="login-error-icon">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p>{error}</p>
                </div>
              )}
              
              <div className="login-form-fields">
                <div className="form-group">
                  <label htmlFor="username" className="form-label">Användarnamn</label>
                  <div className="login-input-container">
                    <div className="login-input-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <input
                      id="username"
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="form-input login-input"
                      placeholder="Ange ditt användarnamn"
                      required
                    />
                  </div>
                </div>
                
                <div className="form-group">
                  <label htmlFor="password" className="form-label">Lösenord</label>
                  <div className="login-input-container">
                    <div className="login-input-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="form-input login-input"
                      placeholder="Ange ditt lösenord"
                      required
                    />
                  </div>
                </div>
              </div>
              
              <div className="login-form-actions">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="action-button primary login-submit-button"
                >
                  {isLoading ? (
                    <>
                      <div className="login-loading-spinner">
                        <svg viewBox="0 0 24 24">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" opacity="0.25"></circle>
                          <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" opacity="0.75"></path>
                        </svg>
                      </div>
                      Loggar in...
                    </>
                  ) : (
                    <>
                      <svg className="login-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                      </svg>
                      Logga in
                    </>
                  )}
                </button>
                
                <div className="login-divider">
                  <span>eller</span>
                </div>
                
                <button
                  type="button"
                  onClick={handleQuickLogin}
                  className="action-button secondary login-quick-button"
                >
                  <svg className="login-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Snabbinloggning (admin)
                </button>
              </div>
            </form>

            <div className="login-security-info">
              <div className="login-security-header">
                <div className="login-security-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h4>Enterprise-säkerhet aktiverad</h4>
              </div>
              <div className="login-security-features">
                <div className="login-security-feature">
                  <svg className="login-check-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>JWT-autentisering & rollbaserade behörigheter</span>
                </div>
                <div className="login-security-feature">
                  <svg className="login-check-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>Rate limiting & säkerhetsövervakning</span>
                </div>
                <div className="login-security-feature">
                  <svg className="login-check-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>HTTPS, CSP & moderna säkerhetsheaders</span>
                </div>
              </div>
              
              <div className="login-default-credentials">
                <div className="login-warning-header">
                  <svg className="login-warning-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span>Standard-inloggning</span>
                </div>
                <div className="login-credentials">
                  <div>👤 Användare: <code>admin</code></div>
                  <div>🔑 Lösenord: <code>admin123!</code></div>
                  <div className="login-warning-text">⚠️ Ändra lösenordet efter första inloggningen</div>
                </div>
              </div>
            </div>
            
            <div className="login-footer">
              <div className="login-footer-badges">
                <div className="login-footer-badge">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                  <span>SSL-krypterad</span>
                </div>
                <div className="login-footer-badge">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Enterprise-säkerhet</span>
                </div>
                <div className="login-footer-badge">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <span>24/7 övervakning</span>
                </div>
              </div>
              <p className="login-copyright">© 2025 Automation Hub • Alla rättigheter förbehållna</p>
            </div>
          </div>
        </div>

        {/* Right side - Branding */}
        <div className="login-branding-section">
          <div className="login-branding-container">
            <div className="login-branding-content">
              <div className="login-branding-header">
                <h1>Automation Hub</h1>
                <p>Kraftfull RPA-plattform för automatisering av affärsprocesser</p>
              </div>
              
              <div className="login-features">
                <div className="login-feature">
                  <div className="login-feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Visuell flödesdesigner</span>
                </div>
                
                <div className="login-feature">
                  <div className="login-feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>AI-assisterad automatisering</span>
                </div>
                
                <div className="login-feature">
                  <div className="login-feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>Enterprise-säkerhet</span>
                </div>
              </div>
            </div>
            
            <div className="login-testimonial">
              <div className="login-testimonial-content">
                <p>"Automation Hub har revolutionerat våra affärsprocesser och sparat oss hundratals arbetstimmar varje månad."</p>
                <cite>— Nöjd kund</cite>
              </div>
            </div>
          </div>
          
          <div className="login-branding-decorations">
            <div className="login-decoration login-decoration-1"></div>
            <div className="login-decoration login-decoration-2"></div>
            <div className="login-decoration login-decoration-3"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPrompt;
