import { ReactNode, useEffect, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { AuthStatus } from './AuthStatus'

interface LayoutProps {
  children: ReactNode
}

export function Layout({ children }: LayoutProps) {
  const location = useLocation()
  const [transitionState, setTransitionState] = useState<'entered' | 'entering'>('entered')

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  // Check if we're in flow editor (new flow or editing existing flow)
  const isInFlowEditor = location.pathname === '/flows/new' ||
                        (location.pathname.startsWith('/flows/') && location.pathname !== '/flows')

  // Handle page transitions
  useEffect(() => {
    setTransitionState('entering')

    const timer = setTimeout(() => {
      setTransitionState('entered')
    }, 50)

    return () => clearTimeout(timer)
  }, [location.pathname])

  const navigationItems = [
    { path: '/', icon: 'Layout', label: 'Översikt', description: 'Översikt över aktiviteter' },
    { path: '/flows', icon: 'FlowChart', label: 'Flöden', description: 'Hantera automatiseringsflöden' },
    { path: '/schedules', icon: 'Clock', label: 'Scheman', description: 'Schemalagd automatisering' },
    { path: '/executions', icon: 'ListChecks', label: 'Loggar', description: 'Övervaka flödeskörningar' },
    { path: '/customers', icon: 'Building', label: 'Kunder', description: 'Hantera kundinformation' },
    { path: '/credentials', icon: 'Key', label: 'Inlogg', description: 'Hantera inloggningsuppgifter' },
    { path: '/settings', icon: 'Gear', label: 'Inställningar', description: 'Applikationsinställningar' }
  ]



  return (
    <div className="design-root">
      <div className="layout-container">
        <div className="layout-wrapper">
          {/* Show navigation sidebar only when NOT in flow editor */}
          {!isInFlowEditor && (
            <div className="sidebar-container">
              <div className="sidebar-content">
                <div className="sidebar-nav">
                  <div className="flex justify-between items-center mb-4">
                    <h1 className="sidebar-title">Automation Hub</h1>
                  </div>
                  <div className="nav-items">
                    {navigationItems.map((item) => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={`nav-item ${isActive(item.path) ? 'active' : ''}`}
                      >
                        <div className="nav-icon">
                          {item.icon === 'Layout' && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                              <path d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM40,56H216V96H40ZM216,200H112V112H216v88Z"></path>
                            </svg>
                          )}
                          {item.icon === 'FlowChart' && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="#000000" viewBox="0 0 256 256"><path d="M160,112h48a16,16,0,0,0,16-16V48a16,16,0,0,0-16-16H160a16,16,0,0,0-16,16V64H128a24,24,0,0,0-24,24v32H72v-8A16,16,0,0,0,56,96H24A16,16,0,0,0,8,112v32a16,16,0,0,0,16,16H56a16,16,0,0,0,16-16v-8h32v32a24,24,0,0,0,24,24h16v16a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V160a16,16,0,0,0-16-16H160a16,16,0,0,0-16,16v16H128a8,8,0,0,1-8-8V88a8,8,0,0,1,8-8h16V96A16,16,0,0,0,160,112ZM56,144H24V112H56v32Zm104,16h48v48H160Zm0-112h48V96H160Z"></path></svg>
                          )}
                          {item.icon === 'Clock' && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                              <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm64-88a8,8,0,0,1-8,8H128a8,8,0,0,1-8-8V72a8,8,0,0,1,16,0v48h48A8,8,0,0,1,192,128Z"></path>
                            </svg>
                          )}
                          {item.icon === 'ListChecks' && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                              <path d="M224,128a8,8,0,0,1-8,8H128a8,8,0,0,1,0-16h88A8,8,0,0,1,224,128ZM128,72h88a8,8,0,0,0,0-16H128a8,8,0,0,0,0,16Zm88,112H128a8,8,0,0,0,0,16h88a8,8,0,0,0,0-16ZM82.34,42.34,56,68.69,45.66,58.34A8,8,0,0,0,34.34,69.66l16,16a8,8,0,0,0,11.32,0l32-32A8,8,0,0,0,82.34,42.34Zm0,64L56,132.69,45.66,122.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Zm0,64L56,196.69,45.66,186.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Z"></path>
                            </svg>
                          )}
                          {item.icon === 'Building' && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                              <path d="M240,208H224V96a16,16,0,0,0-16-16H144V32a16,16,0,0,0-16-16H48A16,16,0,0,0,32,32V208H16a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16ZM208,96V208H144V96ZM48,32h80V208H48ZM112,72V88a8,8,0,0,1-16,0V72a8,8,0,0,1,16,0Zm-32,0V88a8,8,0,0,1-16,0V72a8,8,0,0,1,16,0Zm0,40v16a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm32,0v16a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm0,40v16a8,8,0,0,1-16,0V152a8,8,0,0,1,16,0Zm-32,0v16a8,8,0,0,1-16,0V152a8,8,0,0,1,16,0Zm96-16v16a8,8,0,0,1-16,0V136a8,8,0,0,1,16,0Zm0-40v16a8,8,0,0,1-16,0V96a8,8,0,0,1,16,0Z"></path>
                            </svg>
                          )}
                          {item.icon === 'Key' && (
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><rect width="256" height="256" fill="none"/><path d="M93.17,122.83A71.68,71.68,0,0,1,88,95.91c0-38.58,31.08-70.64,69.64-71.87A72,72,0,0,1,232,98.36C230.73,136.92,198.67,168,160.09,168a71.68,71.68,0,0,1-26.92-5.17h0L120,176H96v24H72v24H40a8,8,0,0,1-8-8V187.31a8,8,0,0,1,2.34-5.65l58.83-58.83Z" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="16"/><circle cx="180" cy="76" r="12"/></svg>
                          )}
                          {item.icon === 'Gear' && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                              <path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z"></path>
                            </svg>
                          )}
                        </div>
                        <p className="nav-label">{item.label}</p>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          <main className={`main-content-wrapper ${isInFlowEditor ? 'flow-editor' : ''}`}>
            <div className="page-wrapper">
              <div className={`page-content page-${transitionState}`}>
                {children}
              </div>
            </div>

            {/* User menu at bottom of page */}
            {!isInFlowEditor && (
              <div className="page-user-menu">
                <AuthStatus />
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  )
}
